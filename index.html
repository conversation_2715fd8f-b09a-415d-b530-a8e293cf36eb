<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCEVAL Leaderboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>MCEVAL</h1>
                    <span class="subtitle">Multiple Choice Evaluation</span>
                </div>
                <nav class="nav">
                    <a href="#" class="nav-link active">
                        <i class="fas fa-trophy"></i>
                        Leaderboard
                    </a>
                    <a href="#" class="nav-link">
                        <i class="fas fa-info-circle"></i>
                        About
                    </a>
                    <a href="#" class="nav-link">
                        <i class="fas fa-upload"></i>
                        Submit
                    </a>
                    <a href="#" class="nav-link">
                        <i class="fab fa-github"></i>
                        GitHub
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <div class="hero">
                <h2 class="hero-title">MCEVAL Leaderboard</h2>
                <p class="hero-description">
                    A comprehensive evaluation benchmark for multiple-choice questions across various domains.
                    Track the performance of state-of-the-art language models.
                </p>
            </div>

            <div class="controls">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="Search models...">
                </div>
                <div class="filters">
                    <select id="categoryFilter">
                        <option value="">All Categories</option>
                        <option value="open">Open Source</option>
                        <option value="closed">Closed Source</option>
                        <option value="medical">Medical</option>
                        <option value="baseline">Baseline</option>
                    </select>
                    <button id="refreshBtn" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>

            <div class="leaderboard-container">
                <div class="table-wrapper">
                    <table class="leaderboard-table" id="leaderboardTable">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="model">
                                    Models <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="rank">
                                    Rank <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="dd_acc">
                                    DD<br>Acc ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="dd_f1">
                                    DD<br>F1 ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="vra_acc">
                                    VRA<br>Acc ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="vra_f1">
                                    VRA<br>F1 ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="ll_acc">
                                    LL<br>Acc ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="od_acc">
                                    OD<br>Acc ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="kd_acc">
                                    KD<br>Acc ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="rmse">
                                    CVE<br>RMSE ↓ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="mae">
                                    CVE<br>MAE ↓ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="percent_4">
                                    CVE<br>%_tol ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="bleu">
                                    RG<br>BLEU% ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="rouge">
                                    RG<br>Rouge% ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="bert">
                                    RG<br>BERT% ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="bleu2">
                                    CG<br>BLEU% ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="rouge2">
                                    CG<br>Rouge% ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="bert2">
                                    CG<br>BERT% ↑ <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="u2score">
                                    U2-Score ↑ <i class="fas fa-sort"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="leaderboardBody">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <h3>Total Models</h3>
                    <span id="totalModels">-</span>
                </div>
                <div class="stat-card">
                    <h3>Best Score</h3>
                    <span id="bestScore">-</span>
                </div>
                <div class="stat-card">
                    <h3>Last Updated</h3>
                    <span id="lastUpdated">-</span>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 MCEVAL. All rights reserved.</p>
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Contact</a>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
