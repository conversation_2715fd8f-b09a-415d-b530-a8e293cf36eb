// Sample data for the leaderboard
const leaderboardData = [
    {
        rank: 1,
        model: "GPT-4",
        organization: "OpenAI",
        overall: 86.4,
        stem: 83.1,
        humanities: 88.2,
        social: 87.5,
        other: 86.9,
        date: "2024-01-15",
        category: "closed"
    },
    {
        rank: 2,
        model: "Claude-3 Opus",
        organization: "Anthropic",
        overall: 84.7,
        stem: 81.3,
        humanities: 86.8,
        social: 85.2,
        other: 85.5,
        date: "2024-01-10",
        category: "closed"
    },
    {
        rank: 3,
        model: "Gemini Ultra",
        organization: "Google",
        overall: 83.2,
        stem: 80.5,
        humanities: 84.9,
        social: 83.8,
        other: 84.1,
        date: "2024-01-08",
        category: "closed"
    },
    {
        rank: 4,
        model: "LLaMA-2-70B",
        organization: "Meta",
        overall: 78.9,
        stem: 76.2,
        humanities: 80.1,
        social: 79.5,
        other: 79.8,
        date: "2023-12-20",
        category: "open"
    },
    {
        rank: 5,
        model: "PaLM-2",
        organization: "Google",
        overall: 77.3,
        stem: 74.8,
        humanities: 78.9,
        social: 77.8,
        other: 78.2,
        date: "2023-12-15",
        category: "closed"
    },
    {
        rank: 6,
        model: "Vicuna-33B",
        organization: "LMSYS",
        overall: 73.5,
        stem: 70.2,
        humanities: 75.8,
        social: 74.1,
        other: 74.4,
        date: "2023-11-30",
        category: "open"
    },
    {
        rank: 7,
        model: "ChatGLM3-6B",
        organization: "Tsinghua",
        overall: 69.8,
        stem: 66.5,
        humanities: 72.1,
        social: 70.3,
        other: 70.8,
        date: "2023-11-25",
        category: "academic"
    },
    {
        rank: 8,
        model: "Alpaca-13B",
        organization: "Stanford",
        overall: 65.2,
        stem: 62.1,
        humanities: 67.8,
        social: 66.0,
        other: 66.5,
        date: "2023-11-20",
        category: "academic"
    }
];

let currentData = [...leaderboardData];
let sortColumn = 'rank';
let sortDirection = 'asc';

// DOM elements
const searchInput = document.getElementById('searchInput');
const categoryFilter = document.getElementById('categoryFilter');
const refreshBtn = document.getElementById('refreshBtn');
const leaderboardBody = document.getElementById('leaderboardBody');
const totalModelsSpan = document.getElementById('totalModels');
const bestScoreSpan = document.getElementById('bestScore');
const lastUpdatedSpan = document.getElementById('lastUpdated');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    renderTable();
    updateStats();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    
    // Category filter
    categoryFilter.addEventListener('change', handleFilter);
    
    // Refresh button
    refreshBtn.addEventListener('click', handleRefresh);
    
    // Table sorting
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', () => {
            const column = header.dataset.column;
            handleSort(column);
        });
    });
}

// Handle search
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    const filteredData = leaderboardData.filter(item => 
        item.model.toLowerCase().includes(searchTerm) ||
        item.organization.toLowerCase().includes(searchTerm)
    );
    
    currentData = filteredData;
    applyCurrentFilter();
    renderTable();
    updateStats();
}

// Handle category filter
function handleFilter() {
    applyCurrentFilter();
    renderTable();
    updateStats();
}

function applyCurrentFilter() {
    const category = categoryFilter.value;
    if (category) {
        currentData = currentData.filter(item => item.category === category);
    }
}

// Handle refresh
function handleRefresh() {
    // Add loading animation
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Refreshing...';
    
    setTimeout(() => {
        // Reset data and filters
        currentData = [...leaderboardData];
        searchInput.value = '';
        categoryFilter.value = '';
        sortColumn = 'rank';
        sortDirection = 'asc';
        
        renderTable();
        updateStats();
        
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
    }, 1000);
}

// Handle sorting
function handleSort(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }
    
    currentData.sort((a, b) => {
        let aVal = a[column];
        let bVal = b[column];
        
        // Handle different data types
        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }
        
        if (column === 'date') {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
        }
        
        if (sortDirection === 'asc') {
            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        } else {
            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
        }
    });
    
    // Update sort indicators
    document.querySelectorAll('.sortable').forEach(header => {
        header.classList.remove('sorted');
        const icon = header.querySelector('i');
        icon.className = 'fas fa-sort';
    });
    
    const currentHeader = document.querySelector(`[data-column="${column}"]`);
    currentHeader.classList.add('sorted');
    const currentIcon = currentHeader.querySelector('i');
    currentIcon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    
    renderTable();
}

// Render the table
function renderTable() {
    leaderboardBody.innerHTML = '';
    
    currentData.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="rank-cell">#${item.rank}</td>
            <td class="model-cell">${item.model}</td>
            <td class="organization-cell">${item.organization}</td>
            <td class="score-cell">${item.overall.toFixed(1)}%</td>
            <td class="score-cell">${item.stem.toFixed(1)}%</td>
            <td class="score-cell">${item.humanities.toFixed(1)}%</td>
            <td class="score-cell">${item.social.toFixed(1)}%</td>
            <td class="score-cell">${item.other.toFixed(1)}%</td>
            <td class="date-cell">${formatDate(item.date)}</td>
        `;
        leaderboardBody.appendChild(row);
    });
}

// Update statistics
function updateStats() {
    totalModelsSpan.textContent = currentData.length;
    
    if (currentData.length > 0) {
        const bestScore = Math.max(...currentData.map(item => item.overall));
        bestScoreSpan.textContent = `${bestScore.toFixed(1)}%`;
    } else {
        bestScoreSpan.textContent = '-';
    }
    
    lastUpdatedSpan.textContent = formatDate(new Date().toISOString().split('T')[0]);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Add some animation effects
function addRowAnimation() {
    const rows = document.querySelectorAll('#leaderboardBody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            row.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
}
