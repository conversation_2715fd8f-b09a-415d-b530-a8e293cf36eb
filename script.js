// Sample data for the leaderboard
const leaderboardData = [
    {
        rank: 1,
        model: "Random Guessing",
        organization: "Baseline",
        overall: 21.25,
        stem: 20.0,
        humanities: 22.5,
        social: 21.0,
        other: 21.8,
        date: "2024-01-01",
        category: "baseline"
    },
    {
        rank: 2,
        model: "MindGPT-Med",
        organization: "Medical AI",
        overall: 23.75,
        stem: 24.0,
        humanities: 23.5,
        social: 24.2,
        other: 23.3,
        date: "2024-01-15",
        category: "medical"
    },
    {
        rank: 3,
        model: "MedDx",
        organization: "Medical AI",
        overall: 25.73,
        stem: 25.8,
        humanities: 25.7,
        social: 25.9,
        other: 25.6,
        date: "2024-01-15",
        category: "medical"
    },
    {
        rank: 4,
        model: "Qwen-2.5-VL-3B-Instruct",
        organization: "Alibaba",
        overall: 30.5,
        stem: 29.8,
        humanities: 31.2,
        social: 30.7,
        other: 30.3,
        date: "2024-02-01",
        category: "open"
    },
    {
        rank: 5,
        model: "Qwen-2.5-VL-7B-Instruct",
        organization: "Alibaba",
        overall: 32.95,
        stem: 32.1,
        humanities: 33.8,
        social: 33.2,
        other: 32.7,
        date: "2024-02-01",
        category: "open"
    },
    {
        rank: 6,
        model: "Qwen-2.5-VL-32B-Instruct",
        organization: "Alibaba",
        overall: 24.39,
        stem: 24.8,
        humanities: 24.0,
        social: 24.5,
        other: 24.3,
        date: "2024-02-01",
        category: "open"
    },
    {
        rank: 7,
        model: "Qwen-2.5-VL-72B-Instruct",
        organization: "Alibaba",
        overall: 24.21,
        stem: 24.5,
        humanities: 23.9,
        social: 24.3,
        other: 24.1,
        date: "2024-02-01",
        category: "open"
    },
    {
        rank: 8,
        model: "DeepSeek-V12",
        organization: "DeepSeek",
        overall: 26.50,
        stem: 26.8,
        humanities: 26.2,
        social: 26.7,
        other: 26.3,
        date: "2024-01-20",
        category: "open"
    },
    {
        rank: 9,
        model: "InternVL-2.0B-Instruct",
        organization: "InternLM",
        overall: 25.66,
        stem: 25.9,
        humanities: 25.4,
        social: 25.8,
        other: 25.5,
        date: "2024-01-25",
        category: "open"
    },
    {
        rank: 10,
        model: "LLaVA-1.5-13B",
        organization: "Microsoft",
        overall: 23.78,
        stem: 24.1,
        humanities: 23.5,
        social: 23.9,
        other: 23.6,
        date: "2024-01-10",
        category: "open"
    },
    {
        rank: 11,
        model: "Phi-3-Multimodal-4k-Instruct",
        organization: "Microsoft",
        overall: 23.68,
        stem: 23.9,
        humanities: 23.5,
        social: 23.8,
        other: 23.5,
        date: "2024-01-12",
        category: "open"
    },
    {
        rank: 12,
        model: "Mistral-Small-1.5-24B-Inst",
        organization: "Mistral AI",
        overall: 23.50,
        stem: 23.7,
        humanities: 23.3,
        social: 23.6,
        other: 23.4,
        date: "2024-01-18",
        category: "open"
    },
    {
        rank: 13,
        model: "Dolphin-2.5-Vision-Pro-12k",
        organization: "Dolphin AI",
        overall: 25.67,
        stem: 25.9,
        humanities: 25.4,
        social: 25.8,
        other: 25.6,
        date: "2024-02-05",
        category: "open"
    },
    {
        rank: 14,
        model: "GPT-4o-Mini",
        organization: "OpenAI",
        overall: 25.86,
        stem: 26.1,
        humanities: 25.6,
        social: 25.9,
        other: 25.8,
        date: "2024-02-10",
        category: "closed"
    },
    {
        rank: 15,
        model: "GPT-4o",
        organization: "OpenAI",
        overall: 22.53,
        stem: 22.8,
        humanities: 22.3,
        social: 22.6,
        other: 22.4,
        date: "2024-02-08",
        category: "closed"
    },
    {
        rank: 16,
        model: "Gemini-1.5-Pro",
        organization: "Google",
        overall: 19.99,
        stem: 20.2,
        humanities: 19.8,
        social: 20.0,
        other: 19.9,
        date: "2024-02-03",
        category: "closed"
    },
    {
        rank: 17,
        model: "Gemini-1.5-Pro-Preview",
        organization: "Google",
        overall: 29.68,
        stem: 30.1,
        humanities: 29.3,
        social: 29.8,
        other: 29.5,
        date: "2024-02-03",
        category: "closed"
    },
    {
        rank: 18,
        model: "Claude-3.5-Sonnet",
        organization: "Anthropic",
        overall: 19.96,
        stem: 20.3,
        humanities: 19.6,
        social: 20.1,
        other: 19.8,
        date: "2024-02-12",
        category: "closed"
    },
    {
        rank: 19,
        model: "Qwen-Max",
        organization: "Alibaba",
        overall: 24.45,
        stem: 24.7,
        humanities: 24.2,
        social: 24.5,
        other: 24.4,
        date: "2024-02-01",
        category: "closed"
    },
    {
        rank: 20,
        model: "Dolphin-VL",
        organization: "Dolphin AI",
        overall: 28.41,
        stem: 28.7,
        humanities: 28.1,
        social: 28.5,
        other: 28.3,
        date: "2024-02-05",
        category: "open"
    }
];

let currentData = [...leaderboardData];
let sortColumn = 'rank';
let sortDirection = 'asc';

// DOM elements
const searchInput = document.getElementById('searchInput');
const categoryFilter = document.getElementById('categoryFilter');
const refreshBtn = document.getElementById('refreshBtn');
const leaderboardBody = document.getElementById('leaderboardBody');
const totalModelsSpan = document.getElementById('totalModels');
const bestScoreSpan = document.getElementById('bestScore');
const lastUpdatedSpan = document.getElementById('lastUpdated');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    renderTable();
    updateStats();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    
    // Category filter
    categoryFilter.addEventListener('change', handleFilter);
    
    // Refresh button
    refreshBtn.addEventListener('click', handleRefresh);
    
    // Table sorting
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', () => {
            const column = header.dataset.column;
            handleSort(column);
        });
    });
}

// Handle search
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    const filteredData = leaderboardData.filter(item => 
        item.model.toLowerCase().includes(searchTerm) ||
        item.organization.toLowerCase().includes(searchTerm)
    );
    
    currentData = filteredData;
    applyCurrentFilter();
    renderTable();
    updateStats();
}

// Handle category filter
function handleFilter() {
    applyCurrentFilter();
    renderTable();
    updateStats();
}

function applyCurrentFilter() {
    const category = categoryFilter.value;
    if (category) {
        currentData = currentData.filter(item => item.category === category);
    }
}

// Handle refresh
function handleRefresh() {
    // Add loading animation
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Refreshing...';
    
    setTimeout(() => {
        // Reset data and filters
        currentData = [...leaderboardData];
        searchInput.value = '';
        categoryFilter.value = '';
        sortColumn = 'rank';
        sortDirection = 'asc';
        
        renderTable();
        updateStats();
        
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
    }, 1000);
}

// Handle sorting
function handleSort(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }
    
    currentData.sort((a, b) => {
        let aVal = a[column];
        let bVal = b[column];
        
        // Handle different data types
        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }
        
        if (column === 'date') {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
        }
        
        if (sortDirection === 'asc') {
            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        } else {
            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
        }
    });
    
    // Update sort indicators
    document.querySelectorAll('.sortable').forEach(header => {
        header.classList.remove('sorted');
        const icon = header.querySelector('i');
        icon.className = 'fas fa-sort';
    });
    
    const currentHeader = document.querySelector(`[data-column="${column}"]`);
    currentHeader.classList.add('sorted');
    const currentIcon = currentHeader.querySelector('i');
    currentIcon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    
    renderTable();
}

// Render the table
function renderTable() {
    leaderboardBody.innerHTML = '';
    
    currentData.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="rank-cell">#${item.rank}</td>
            <td class="model-cell">${item.model}</td>
            <td class="organization-cell">${item.organization}</td>
            <td class="score-cell">${item.overall.toFixed(1)}%</td>
            <td class="score-cell">${item.stem.toFixed(1)}%</td>
            <td class="score-cell">${item.humanities.toFixed(1)}%</td>
            <td class="score-cell">${item.social.toFixed(1)}%</td>
            <td class="score-cell">${item.other.toFixed(1)}%</td>
            <td class="date-cell">${formatDate(item.date)}</td>
        `;
        leaderboardBody.appendChild(row);
    });
}

// Update statistics
function updateStats() {
    totalModelsSpan.textContent = currentData.length;
    
    if (currentData.length > 0) {
        const bestScore = Math.max(...currentData.map(item => item.overall));
        bestScoreSpan.textContent = `${bestScore.toFixed(1)}%`;
    } else {
        bestScoreSpan.textContent = '-';
    }
    
    lastUpdatedSpan.textContent = formatDate(new Date().toISOString().split('T')[0]);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Add some animation effects
function addRowAnimation() {
    const rows = document.querySelectorAll('#leaderboardBody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            row.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
}
